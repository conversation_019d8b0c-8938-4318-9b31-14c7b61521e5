"""
混合服务器 - 同时支持MCP协议和HTTP协议
"""

import asyncio
import uvicorn
import threading
from multiprocessing import Process
import signal
import sys
import time

# 导入MCP服务器
from fastmcp_server import mcp, sse_app_with_logging

# 导入HTTP API
from api.routes import create_http_app


class HybridServer:
    """混合服务器类，同时运行MCP和HTTP服务"""
    
    def __init__(self, 
                 mcp_host: str = "0.0.0.0", 
                 mcp_port: int = 8000,
                 http_host: str = "0.0.0.0", 
                 http_port: int = 8001):
        self.mcp_host = mcp_host
        self.mcp_port = mcp_port
        self.http_host = http_host
        self.http_port = http_port
        self.mcp_process = None
        self.http_process = None
        self.running = False
    
    def start_mcp_server(self):
        """启动MCP服务器"""
        print(f"🚀 启动MCP服务器: {self.mcp_host}:{self.mcp_port}")
        print("   支持协议: SSE, stdio, streamable-http")
        
        try:
            app = sse_app_with_logging()
            uvicorn.run(
                app, 
                host=self.mcp_host, 
                port=self.mcp_port, 
                log_level="info",
                access_log=True
            )
        except Exception as e:
            print(f"❌ MCP服务器启动失败: {e}")
    
    def start_http_server(self):
        """启动HTTP API服务器"""
        print(f"🚀 启动HTTP API服务器: {self.http_host}:{self.http_port}")
        print("   支持协议: HTTP RESTful API")
        print(f"   API文档: http://{self.http_host}:{self.http_port}/docs")
        
        try:
            app = create_http_app()
            uvicorn.run(
                app, 
                host=self.http_host, 
                port=self.http_port, 
                log_level="info",
                access_log=True
            )
        except Exception as e:
            print(f"❌ HTTP服务器启动失败: {e}")
    
    def start_both_servers(self):
        """同时启动两个服务器"""
        print("=" * 60)
        print("🌟 MCP混合服务器启动中...")
        print("=" * 60)
        
        # 创建进程启动两个服务器
        self.mcp_process = Process(target=self.start_mcp_server)
        self.http_process = Process(target=self.start_http_server)
        
        # 启动进程
        self.mcp_process.start()
        time.sleep(1)  # 稍等一下再启动HTTP服务器
        self.http_process.start()
        
        self.running = True
        
        print("=" * 60)
        print("✅ 混合服务器启动完成!")
        print(f"📡 MCP服务器: http://{self.mcp_host}:{self.mcp_port}")
        print(f"🌐 HTTP API服务器: http://{self.http_host}:{self.http_port}")
        print(f"📚 API文档: http://{self.http_host}:{self.http_port}/docs")
        print("=" * 60)
        print("按 Ctrl+C 停止服务器")
        
        # 等待进程结束
        try:
            self.mcp_process.join()
            self.http_process.join()
        except KeyboardInterrupt:
            self.stop_servers()
    
    def stop_servers(self):
        """停止所有服务器"""
        print("\n🛑 正在停止服务器...")
        
        if self.mcp_process and self.mcp_process.is_alive():
            print("   停止MCP服务器...")
            self.mcp_process.terminate()
            self.mcp_process.join(timeout=5)
            if self.mcp_process.is_alive():
                self.mcp_process.kill()
        
        if self.http_process and self.http_process.is_alive():
            print("   停止HTTP服务器...")
            self.http_process.terminate()
            self.http_process.join(timeout=5)
            if self.http_process.is_alive():
                self.http_process.kill()
        
        self.running = False
        print("✅ 所有服务器已停止")
    
    def start_mcp_only(self):
        """仅启动MCP服务器"""
        print("🚀 启动MCP服务器（仅MCP模式）")
        self.start_mcp_server()
    
    def start_http_only(self):
        """仅启动HTTP服务器"""
        print("🚀 启动HTTP API服务器（仅HTTP模式）")
        self.start_http_server()


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n收到信号 {signum}，正在关闭服务器...")
    sys.exit(0)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="MCP混合服务器")
    parser.add_argument("--mode", choices=["hybrid", "mcp", "http"], default="hybrid",
                       help="服务器模式: hybrid(混合), mcp(仅MCP), http(仅HTTP)")
    parser.add_argument("--mcp-host", default="0.0.0.0", help="MCP服务器主机")
    parser.add_argument("--mcp-port", type=int, default=8000, help="MCP服务器端口")
    parser.add_argument("--http-host", default="0.0.0.0", help="HTTP服务器主机")
    parser.add_argument("--http-port", type=int, default=8001, help="HTTP服务器端口")
    
    args = parser.parse_args()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建服务器实例
    server = HybridServer(
        mcp_host=args.mcp_host,
        mcp_port=args.mcp_port,
        http_host=args.http_host,
        http_port=args.http_port
    )
    
    try:
        if args.mode == "hybrid":
            server.start_both_servers()
        elif args.mode == "mcp":
            server.start_mcp_only()
        elif args.mode == "http":
            server.start_http_only()
    except KeyboardInterrupt:
        server.stop_servers()
    except Exception as e:
        print(f"❌ 服务器运行出错: {e}")
        server.stop_servers()


if __name__ == "__main__":
    main()
