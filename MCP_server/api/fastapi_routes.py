"""
FastAPI HTTP接口路由
提供RESTful API接口，封装MCP工具函数
"""

from fastapi import FastAPI, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Optional, List, Any
from datetime import datetime
import json

# 导入MCP工具函数
from mcp_tools.testcase_management import (
    create_testcase_set,
    query_testcase_set_info,
    list_all_testcase_sets,
    add_testcase_to_set,
    list_testcases_in_set,
    modify_testcase_details
)
from mcp_tools.prompt_tools import generate_testcase_conversion_prompt
from mcp_tools.feishu_doc import fetch_feishu_doc_content


# Pydantic模型定义

class TestCaseSetCreate(BaseModel):
    """创建测试用例集的请求模型"""
    name: str = Field(..., description="测试用例集名称")
    prd_doc_link: str = Field(..., description="PRD文档链接")
    prd_doc_title: Optional[str] = Field(None, description="PRD文档标题")
    prd_doc_content: Optional[str] = Field(None, description="PRD文档文本内容")
    prd_doc_updated_at: Optional[datetime] = Field(None, description="PRD文档更新日期")
    prd_doc_type: Optional[str] = Field(None, description="PRD文档类型", pattern="^(feishu_wiki|feishu_doc|local_file)$")
    prd_doc_format: Optional[str] = Field(None, description="PRD文档格式", pattern="^(markdown|pdf|txt|docx|html)$")
    author: Optional[str] = Field(None, description="作者")


class TestCaseCreate(BaseModel):
    """创建测试用例的请求模型"""
    test_case_set_uuid: str = Field(..., description="测试用例集UUID")
    title: str = Field(..., description="测试用例标题")
    priority: str = Field(..., description="优先级", pattern="^(P0|P1|P2)$")
    description: Optional[str] = Field(None, description="测试用例描述")


class TestCaseUpdate(BaseModel):
    """更新测试用例的请求模型"""
    title: Optional[str] = Field(None, description="测试用例标题")
    priority: Optional[str] = Field(None, description="优先级", pattern="^(P0|P1|P2)$")
    description: Optional[str] = Field(None, description="测试用例描述")
    requires_self_test: Optional[bool] = Field(None, description="是否需要自测")


class PromptRequest(BaseModel):
    """生成提示词的请求模型"""
    document_content: str = Field(..., description="文档内容")


class FeishuDocRequest(BaseModel):
    """获取飞书文档的请求模型"""
    doc_link: str = Field(..., description="飞书文档链接")
    lang: Optional[int] = Field(0, description="语言设置")


class StandardResponse(BaseModel):
    """标准响应模型"""
    status: str = Field(..., description="状态")
    message: str = Field(..., description="消息")
    data: Optional[Any] = Field(None, description="数据")


# 创建FastAPI应用
def create_fastapi_app() -> FastAPI:
    """创建FastAPI应用实例"""
    app = FastAPI(
        title="测试用例管理API",
        description="基于MCP服务的测试用例管理RESTful API",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )

    # 测试用例集相关接口
    @app.post("/api/v1/testcase-sets", response_model=StandardResponse, tags=["测试用例集"])
    async def create_testcase_set_api(request: TestCaseSetCreate):
        """创建测试用例集"""
        try:
            result = create_testcase_set(
                name=request.name,
                prd_doc_link=request.prd_doc_link,
                prd_doc_title=request.prd_doc_title,
                prd_doc_content=request.prd_doc_content,
                prd_doc_updated_at=request.prd_doc_updated_at,
                prd_doc_type=request.prd_doc_type,
                prd_doc_format=request.prd_doc_format,
                author=request.author
            )
            return result
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/api/v1/testcase-sets/{uuid}", response_model=StandardResponse, tags=["测试用例集"])
    async def get_testcase_set_api(uuid: str):
        """获取测试用例集信息"""
        try:
            result = query_testcase_set_info(uuid)
            if result["status"] == "error":
                raise HTTPException(status_code=404, detail=result["message"])
            return result
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/api/v1/testcase-sets", response_model=StandardResponse, tags=["测试用例集"])
    async def list_testcase_sets_api(limit: int = 100, offset: int = 0):
        """获取测试用例集列表"""
        try:
            result = list_all_testcase_sets(limit=limit, offset=offset)
            return result
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    # 测试用例相关接口
    @app.post("/api/v1/testcases", response_model=StandardResponse, tags=["测试用例"])
    async def create_testcase_api(request: TestCaseCreate):
        """创建测试用例"""
        try:
            result = add_testcase_to_set(
                test_case_set_uuid=request.test_case_set_uuid,
                title=request.title,
                priority=request.priority,
                description=request.description
            )
            return result
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/api/v1/testcase-sets/{set_uuid}/testcases", response_model=StandardResponse, tags=["测试用例"])
    async def list_testcases_api(set_uuid: str):
        """获取测试用例集下的测试用例列表"""
        try:
            result = list_testcases_in_set(set_uuid)
            if result["status"] == "error":
                raise HTTPException(status_code=404, detail=result["message"])
            return result
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @app.put("/api/v1/testcases/{uuid}", response_model=StandardResponse, tags=["测试用例"])
    async def update_testcase_api(uuid: str, request: TestCaseUpdate):
        """更新测试用例"""
        try:
            result = modify_testcase_details(
                uuid=uuid,
                title=request.title,
                priority=request.priority,
                description=request.description,
                requires_self_test=request.requires_self_test
            )
            if result["status"] == "error":
                raise HTTPException(status_code=404, detail=result["message"])
            return result
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    # 提示词相关接口
    @app.post("/api/v1/prompts/testcase-conversion", tags=["提示词"])
    async def generate_prompt_api(request: PromptRequest):
        """生成测试用例转换提示词"""
        try:
            prompt = generate_testcase_conversion_prompt(request.document_content)
            return {
                "status": "success",
                "message": "提示词生成成功",
                "data": {"prompt": prompt}
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    # 飞书文档相关接口
    @app.post("/api/v1/feishu/fetch-doc", response_model=StandardResponse, tags=["飞书文档"])
    async def fetch_feishu_doc_api(request: FeishuDocRequest):
        """获取飞书文档内容"""
        try:
            result = fetch_feishu_doc_content(request.doc_link, request.lang)
            if "error" in result:
                raise HTTPException(status_code=400, detail=result["error"])
            return {
                "status": "success",
                "message": "飞书文档获取成功",
                "data": result
            }
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    # 健康检查接口
    @app.get("/health", tags=["系统"])
    async def health_check():
        """健康检查"""
        return {"status": "healthy", "timestamp": datetime.now().isoformat()}

    # API信息接口
    @app.get("/api/info", tags=["系统"])
    async def api_info():
        """API信息"""
        return {
            "name": "测试用例管理API",
            "version": "1.0.0",
            "description": "基于MCP服务的测试用例管理RESTful API",
            "endpoints": {
                "testcase_sets": "/api/v1/testcase-sets",
                "testcases": "/api/v1/testcases",
                "prompts": "/api/v1/prompts",
                "feishu": "/api/v1/feishu",
                "docs": "/docs",
                "health": "/health"
            }
        }

    return app
