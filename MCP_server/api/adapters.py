"""
HTTP接口适配器
处理MCP工具函数与HTTP API之间的格式转换
"""

from typing import Any, Dict, Optional, Callable
from functools import wraps
import traceback
import asyncio
import inspect


class MCPToHTTPAdapter:
    """MCP工具函数到HTTP API的适配器"""
    
    def __init__(self):
        self.function_registry = {}
    
    def register_function(self, name: str, func: Callable, exclude_ctx: bool = True):
        """
        注册MCP工具函数
        
        Args:
            name: 函数名称
            func: MCP工具函数
            exclude_ctx: 是否排除Context参数（默认True）
        """
        self.function_registry[name] = {
            'function': func,
            'exclude_ctx': exclude_ctx
        }
    
    def call_mcp_function(self, name: str, **kwargs) -> Dict[str, Any]:
        """
        调用MCP工具函数并处理结果
        
        Args:
            name: 函数名称
            **kwargs: 函数参数
            
        Returns:
            标准化的响应字典
        """
        if name not in self.function_registry:
            return {
                "status": "error",
                "message": f"Function '{name}' not found",
                "data": None
            }
        
        func_info = self.function_registry[name]
        func = func_info['function']
        exclude_ctx = func_info['exclude_ctx']
        
        try:
            # 获取函数签名
            sig = inspect.signature(func)
            
            # 过滤参数，排除ctx参数
            filtered_kwargs = {}
            for param_name, param in sig.parameters.items():
                if exclude_ctx and param_name == 'ctx':
                    continue
                if param_name in kwargs:
                    filtered_kwargs[param_name] = kwargs[param_name]
            
            # 调用函数
            result = func(**filtered_kwargs)
            
            # 如果结果已经是标准格式，直接返回
            if isinstance(result, dict) and 'status' in result:
                return result
            
            # 否则包装成标准格式
            return {
                "status": "success",
                "message": "操作成功",
                "data": result
            }
            
        except Exception as e:
            error_msg = str(e)
            print(f"[MCPAdapter] Error calling {name}: {error_msg}")
            print(f"[MCPAdapter] Traceback: {traceback.format_exc()}")
            
            return {
                "status": "error",
                "message": f"调用函数失败: {error_msg}",
                "data": None
            }
    
    async def async_call_mcp_function(self, name: str, **kwargs) -> Dict[str, Any]:
        """
        异步调用MCP工具函数
        
        Args:
            name: 函数名称
            **kwargs: 函数参数
            
        Returns:
            标准化的响应字典
        """
        # 在线程池中执行同步函数
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, 
            self.call_mcp_function, 
            name, 
            **kwargs
        )


def create_http_wrapper(mcp_func: Callable, exclude_ctx: bool = True):
    """
    创建MCP函数的HTTP包装器
    
    Args:
        mcp_func: MCP工具函数
        exclude_ctx: 是否排除Context参数
        
    Returns:
        HTTP API兼容的函数
    """
    @wraps(mcp_func)
    def wrapper(**kwargs):
        try:
            # 获取函数签名
            sig = inspect.signature(mcp_func)
            
            # 过滤参数
            filtered_kwargs = {}
            for param_name, param in sig.parameters.items():
                if exclude_ctx and param_name == 'ctx':
                    continue
                if param_name in kwargs:
                    filtered_kwargs[param_name] = kwargs[param_name]
            
            # 调用原函数
            result = mcp_func(**filtered_kwargs)
            
            # 标准化返回格式
            if isinstance(result, dict) and 'status' in result:
                return result
            
            return {
                "status": "success",
                "message": "操作成功",
                "data": result
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"操作失败: {str(e)}",
                "data": None
            }
    
    return wrapper


def validate_response_format(response: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证并标准化响应格式
    
    Args:
        response: 原始响应
        
    Returns:
        标准化的响应
    """
    if not isinstance(response, dict):
        return {
            "status": "error",
            "message": "Invalid response format",
            "data": response
        }
    
    # 确保必要字段存在
    standardized = {
        "status": response.get("status", "success"),
        "message": response.get("message", "操作完成"),
        "data": response.get("data")
    }
    
    # 保留其他字段
    for key, value in response.items():
        if key not in ["status", "message", "data"]:
            standardized[key] = value
    
    return standardized


# 创建全局适配器实例
mcp_adapter = MCPToHTTPAdapter()


def register_mcp_functions():
    """注册所有MCP工具函数到适配器"""
    
    # 导入MCP工具函数
    from mcp_tools.testcase_management import (
        create_testcase_set,
        query_testcase_set_info,
        list_all_testcase_sets,
        add_testcase_to_set,
        list_testcases_in_set,
        modify_testcase_details
    )
    from mcp_tools.prompt_tools import generate_testcase_conversion_prompt
    from mcp_tools.feishu_doc import fetch_feishu_doc_content
    
    # 注册测试用例集管理函数
    mcp_adapter.register_function("create_testcase_set", create_testcase_set)
    mcp_adapter.register_function("query_testcase_set_info", query_testcase_set_info)
    mcp_adapter.register_function("list_all_testcase_sets", list_all_testcase_sets)
    
    # 注册测试用例管理函数
    mcp_adapter.register_function("add_testcase_to_set", add_testcase_to_set)
    mcp_adapter.register_function("list_testcases_in_set", list_testcases_in_set)
    mcp_adapter.register_function("modify_testcase_details", modify_testcase_details)
    
    # 注册提示词函数
    mcp_adapter.register_function("generate_testcase_conversion_prompt", generate_testcase_conversion_prompt)
    
    # 注册文档处理函数
    mcp_adapter.register_function("fetch_feishu_doc_content", fetch_feishu_doc_content)
    
    print("✅ MCP函数适配器注册完成")
    print(f"📋 已注册 {len(mcp_adapter.function_registry)} 个MCP函数")


# 初始化时注册函数
register_mcp_functions()
