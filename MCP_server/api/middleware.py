"""
HTTP中间件
提供CORS支持、请求日志、错误处理等功能
"""

from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
import time
import json
import traceback
import uuid
from typing import Callable


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成请求ID
        request_id = str(uuid.uuid4())[:8]
        start_time = time.time()
        
        # 记录请求开始
        print(f"[HTTP-{request_id}] --> {request.method} {request.url.path}")
        print(f"[HTTP-{request_id}] Client: {request.client.host}:{request.client.port}")
        print(f"[HTTP-{request_id}] User-Agent: {request.headers.get('user-agent', 'Unknown')}")
        
        # 记录查询参数
        if request.url.query:
            print(f"[HTTP-{request_id}] Query: {request.url.query}")
        
        # 记录请求体（仅对POST/PUT请求）
        if request.method in ["POST", "PUT", "PATCH"]:
            try:
                body = await request.body()
                if body:
                    # 尝试解析JSON
                    try:
                        body_json = json.loads(body)
                        print(f"[HTTP-{request_id}] Body: {json.dumps(body_json, ensure_ascii=False, indent=2)}")
                    except json.JSONDecodeError:
                        print(f"[HTTP-{request_id}] Body: {body.decode('utf-8', errors='ignore')[:500]}...")
                
                # 重新构造请求体供后续处理
                async def receive():
                    return {"type": "http.request", "body": body}
                
                request._receive = receive
            except Exception as e:
                print(f"[HTTP-{request_id}] Body read error: {e}")
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应
            print(f"[HTTP-{request_id}] <-- {response.status_code} ({process_time:.3f}s)")
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = f"{process_time:.3f}"
            
            return response
            
        except Exception as e:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录错误
            print(f"[HTTP-{request_id}] <-- ERROR ({process_time:.3f}s)")
            print(f"[HTTP-{request_id}] Exception: {str(e)}")
            print(f"[HTTP-{request_id}] Traceback: {traceback.format_exc()}")
            
            # 返回错误响应
            return JSONResponse(
                status_code=500,
                content={
                    "status": "error",
                    "message": f"Internal server error: {str(e)}",
                    "data": None,
                    "request_id": request_id
                },
                headers={
                    "X-Request-ID": request_id,
                    "X-Process-Time": f"{process_time:.3f}"
                }
            )


class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """全局错误处理中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            response = await call_next(request)
            return response
        except HTTPException as e:
            # FastAPI的HTTP异常，直接传递
            return JSONResponse(
                status_code=e.status_code,
                content={
                    "status": "error",
                    "message": e.detail,
                    "data": None
                }
            )
        except ValueError as e:
            # 参数验证错误
            return JSONResponse(
                status_code=400,
                content={
                    "status": "error",
                    "message": f"参数错误: {str(e)}",
                    "data": None
                }
            )
        except Exception as e:
            # 其他未捕获的异常
            error_msg = str(e)
            print(f"[ErrorHandler] Unhandled exception: {error_msg}")
            print(f"[ErrorHandler] Traceback: {traceback.format_exc()}")
            
            return JSONResponse(
                status_code=500,
                content={
                    "status": "error",
                    "message": f"服务器内部错误: {error_msg}",
                    "data": None
                }
            )


class SecurityMiddleware(BaseHTTPMiddleware):
    """安全中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 添加安全头
        response = await call_next(request)
        
        # 添加安全响应头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        return response


def setup_cors_middleware(app, 
                         allow_origins: list = None,
                         allow_credentials: bool = True,
                         allow_methods: list = None,
                         allow_headers: list = None):
    """
    设置CORS中间件
    
    Args:
        app: FastAPI应用实例
        allow_origins: 允许的源列表
        allow_credentials: 是否允许凭证
        allow_methods: 允许的HTTP方法
        allow_headers: 允许的请求头
    """
    if allow_origins is None:
        allow_origins = ["*"]
    
    if allow_methods is None:
        allow_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"]
    
    if allow_headers is None:
        allow_headers = ["*"]
    
    app.add_middleware(
        CORSMiddleware,
        allow_origins=allow_origins,
        allow_credentials=allow_credentials,
        allow_methods=allow_methods,
        allow_headers=allow_headers,
    )
    
    print("✅ CORS中间件已配置")
    print(f"   - 允许源: {allow_origins}")
    print(f"   - 允许方法: {allow_methods}")
    print(f"   - 允许凭证: {allow_credentials}")


def setup_middleware(app):
    """
    设置所有中间件
    
    Args:
        app: FastAPI应用实例
    """
    # 1. 安全中间件（最外层）
    app.add_middleware(SecurityMiddleware)
    
    # 2. 错误处理中间件
    app.add_middleware(ErrorHandlingMiddleware)
    
    # 3. 请求日志中间件
    app.add_middleware(RequestLoggingMiddleware)
    
    # 4. CORS中间件
    setup_cors_middleware(app)
    
    print("✅ 所有HTTP中间件已配置完成")


# 自定义异常处理器
async def validation_exception_handler(request: Request, exc):
    """参数验证异常处理器"""
    return JSONResponse(
        status_code=422,
        content={
            "status": "error",
            "message": "请求参数验证失败",
            "data": {
                "detail": exc.errors() if hasattr(exc, 'errors') else str(exc)
            }
        }
    )


async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "status": "error",
            "message": exc.detail,
            "data": None
        }
    )


def setup_exception_handlers(app):
    """
    设置异常处理器
    
    Args:
        app: FastAPI应用实例
    """
    from fastapi.exceptions import RequestValidationError
    
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    
    print("✅ 异常处理器已配置完成")
