"""
Pydantic模型定义
用于HTTP API的请求和响应数据验证
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Any
from datetime import datetime


# ==================== 基础响应模型 ====================

class APIResponse(BaseModel):
    """标准API响应格式"""
    status: str = Field(..., description="响应状态: success/error")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")


# ==================== 数学运算相关模型 ====================

class MathOperationRequest(BaseModel):
    """数学运算请求模型"""
    a: float = Field(..., description="第一个操作数")
    b: float = Field(..., description="第二个操作数")


class MathOperationResponse(APIResponse):
    """数学运算响应模型"""
    data: Optional[float] = Field(None, description="运算结果")


# ==================== 文档处理相关模型 ====================

class FeishuDocRequest(BaseModel):
    """飞书文档请求模型"""
    doc_link: str = Field(..., description="飞书文档链接")
    lang: int = Field(0, description="语言参数，0为中文，1为英文")


class MarkdownToJsonRequest(BaseModel):
    """Markdown转JSON请求模型"""
    markdown_content: str = Field(..., description="Markdown表格内容")


class JsonToMarkdownRequest(BaseModel):
    """JSON转Markdown请求模型"""
    json_data: str = Field(..., description="JSON字符串数据")


# ==================== 测试用例集相关模型 ====================

class TestCaseSetCreateRequest(BaseModel):
    """创建测试用例集请求模型"""
    name: str = Field(..., description="测试用例集名称")
    prd_doc_link: str = Field(..., description="PRD文档链接")
    prd_doc_title: Optional[str] = Field(None, description="PRD文档标题")
    author: Optional[str] = Field(None, description="作者姓名")


class TestCaseSetQueryRequest(BaseModel):
    """查询测试用例集请求模型"""
    uuid: str = Field(..., description="测试用例集UUID")


class TestCaseSetListRequest(BaseModel):
    """测试用例集列表请求模型"""
    limit: int = Field(100, description="每页记录数", ge=1, le=500)
    offset: int = Field(0, description="跳过记录数", ge=0)


class TestCaseSetInfo(BaseModel):
    """测试用例集信息模型"""
    uuid: str
    name: str
    prd_doc_link: str
    prd_doc_title: Optional[str]
    author: Optional[str]
    created_at: Optional[str]
    updated_at: Optional[str]


# ==================== 测试用例相关模型 ====================

class TestCaseCreateRequest(BaseModel):
    """创建测试用例请求模型"""
    test_case_set_uuid: str = Field(..., description="测试用例集UUID")
    title: str = Field(..., description="测试用例标题")
    priority: str = Field(..., description="优先级: P0/P1/P2")
    description: Optional[str] = Field(None, description="测试用例描述")


class TestCaseListRequest(BaseModel):
    """测试用例列表请求模型"""
    test_case_set_uuid: str = Field(..., description="测试用例集UUID")


class TestCaseUpdateRequest(BaseModel):
    """更新测试用例请求模型"""
    uuid: str = Field(..., description="测试用例UUID")
    title: Optional[str] = Field(None, description="测试用例标题")
    priority: Optional[str] = Field(None, description="优先级: P0/P1/P2")
    description: Optional[str] = Field(None, description="测试用例描述")
    requires_self_test: Optional[bool] = Field(None, description="是否需要自测")


class TestCaseInfo(BaseModel):
    """测试用例信息模型"""
    uuid: str
    test_case_set_uuid: str
    title: str
    priority: str
    requires_self_test: bool
    description: Optional[str]
    created_at: Optional[str]
    updated_at: Optional[str]


# ==================== 提示词相关模型 ====================

class PromptRequest(BaseModel):
    """提示词生成请求模型"""
    document_content: str = Field(..., description="文档内容")


class PromptResponse(APIResponse):
    """提示词生成响应模型"""
    data: Optional[dict] = Field(None, description="包含生成的提示词")
