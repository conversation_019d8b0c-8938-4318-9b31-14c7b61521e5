"""
HTTP API路由定义
提供RESTful API接口，封装MCP工具函数
"""

from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import JSONResponse
import traceback
import time

# 导入中间件
from .middleware import setup_middleware, setup_exception_handlers

# 导入模型
from .models import (
    APIResponse, MathOperationRequest, MathOperationResponse,
    FeishuDocRequest, MarkdownToJsonRequest, JsonToMarkdownRequest,
    TestCaseSetCreateRequest, TestCaseSetQueryRequest, TestCaseSetListRequest,
    TestCaseCreateRequest, TestCaseListRequest, TestCaseUpdateRequest,
    PromptRequest, PromptResponse
)

# 导入MCP工具函数
from mcp_tools.testcase_management import (
    create_testcase_set,
    query_testcase_set_info,
    list_all_testcase_sets,
    add_testcase_to_set,
    list_testcases_in_set,
    modify_testcase_details
)
from mcp_tools.prompt_tools import generate_testcase_conversion_prompt
from mcp_tools.feishu_doc import fetch_feishu_doc_content


def create_http_app() -> FastAPI:
    """创建HTTP API应用"""
    
    app = FastAPI(
        title="MCP Server HTTP API",
        description="MCP工具函数的HTTP API接口",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )

    # 设置中间件和异常处理器
    setup_middleware(app)
    setup_exception_handlers(app)
    
    # ==================== 健康检查接口 ====================
    
    @app.get("/health", response_model=APIResponse)
    async def health_check():
        """健康检查接口"""
        return APIResponse(
            status="success",
            message="MCP Server HTTP API is running",
            data={"timestamp": time.time()}
        )
    
    # ==================== 数学运算接口 ====================
    
    @app.post("/api/v1/math/add", response_model=MathOperationResponse, tags=["数学运算"])
    async def add_api(request: MathOperationRequest):
        """加法运算"""
        try:
            # 直接调用运算，不需要MCP上下文
            result = request.a + request.b
            return MathOperationResponse(
                status="success",
                message="加法运算成功",
                data=result
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/v1/math/subtract", response_model=MathOperationResponse, tags=["数学运算"])
    async def subtract_api(request: MathOperationRequest):
        """减法运算"""
        try:
            result = request.a - request.b
            return MathOperationResponse(
                status="success",
                message="减法运算成功",
                data=result
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/v1/math/multiply", response_model=MathOperationResponse, tags=["数学运算"])
    async def multiply_api(request: MathOperationRequest):
        """乘法运算"""
        try:
            result = request.a * request.b
            return MathOperationResponse(
                status="success",
                message="乘法运算成功",
                data=result
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/v1/math/divide", response_model=MathOperationResponse, tags=["数学运算"])
    async def divide_api(request: MathOperationRequest):
        """除法运算"""
        try:
            if request.b == 0:
                return MathOperationResponse(
                    status="error",
                    message="除数不能为零",
                    data=None
                )
            result = request.a / request.b
            return MathOperationResponse(
                status="success",
                message="除法运算成功",
                data=result
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    # ==================== 文档处理接口 ====================
    
    @app.post("/api/v1/docs/feishu", response_model=APIResponse, tags=["文档处理"])
    async def fetch_feishu_doc_api(request: FeishuDocRequest):
        """获取飞书文档内容"""
        try:
            result = fetch_feishu_doc_content(request.doc_link, request.lang)
            return APIResponse(
                status=result.get("status", "success"),
                message=result.get("message", "获取文档成功"),
                data=result.get("data")
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/v1/docs/markdown-to-json", response_model=APIResponse, tags=["文档处理"])
    async def markdown_to_json_api(request: MarkdownToJsonRequest):
        """Markdown表格转JSON"""
        try:
            # 这里需要调用原有的MCP函数
            from fastmcp_server import markdown_table_to_json
            result = markdown_table_to_json(request.markdown_content)
            return APIResponse(
                status=result.get("status", "success"),
                message=result.get("message", "转换成功"),
                data=result.get("data")
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @app.post("/api/v1/docs/json-to-markdown", response_model=APIResponse, tags=["文档处理"])
    async def json_to_markdown_api(request: JsonToMarkdownRequest):
        """JSON转Markdown表格"""
        try:
            # 这里需要调用原有的MCP函数
            from fastmcp_server import json_to_markdown_table
            result = json_to_markdown_table(request.json_data)
            return APIResponse(
                status=result.get("status", "success"),
                message=result.get("message", "转换成功"),
                data=result.get("data")
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    # ==================== 测试用例集管理接口 ====================

    @app.post("/api/v1/testcase-sets", response_model=APIResponse, tags=["测试用例集管理"])
    async def create_testcase_set_api(request: TestCaseSetCreateRequest):
        """创建测试用例集"""
        try:
            result = create_testcase_set(
                name=request.name,
                prd_doc_link=request.prd_doc_link,
                prd_doc_title=request.prd_doc_title,
                author=request.author
            )
            return APIResponse(
                status=result.get("status", "success"),
                message=result.get("message", "创建成功"),
                data=result.get("data")
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/api/v1/testcase-sets/{uuid}", response_model=APIResponse, tags=["测试用例集管理"])
    async def get_testcase_set_api(uuid: str):
        """查询测试用例集信息"""
        try:
            result = query_testcase_set_info(uuid)
            if result.get("status") == "error":
                raise HTTPException(status_code=404, detail=result.get("message"))
            return APIResponse(
                status=result.get("status", "success"),
                message=result.get("message", "查询成功"),
                data=result.get("data")
            )
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/api/v1/testcase-sets", response_model=APIResponse, tags=["测试用例集管理"])
    async def list_testcase_sets_api(limit: int = 100, offset: int = 0):
        """分页查询测试用例集列表"""
        try:
            result = list_all_testcase_sets(limit=limit, offset=offset)
            return APIResponse(
                status=result.get("status", "success"),
                message=result.get("message", "查询成功"),
                data=result.get("data")
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    # ==================== 测试用例管理接口 ====================

    @app.post("/api/v1/testcases", response_model=APIResponse, tags=["测试用例管理"])
    async def create_testcase_api(request: TestCaseCreateRequest):
        """创建测试用例"""
        try:
            result = add_testcase_to_set(
                test_case_set_uuid=request.test_case_set_uuid,
                title=request.title,
                priority=request.priority,
                description=request.description
            )
            return APIResponse(
                status=result.get("status", "success"),
                message=result.get("message", "创建成功"),
                data=result.get("data")
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/api/v1/testcase-sets/{set_uuid}/testcases", response_model=APIResponse, tags=["测试用例管理"])
    async def list_testcases_api(set_uuid: str):
        """获取测试用例集下的所有测试用例"""
        try:
            result = list_testcases_in_set(set_uuid)
            return APIResponse(
                status=result.get("status", "success"),
                message=result.get("message", "查询成功"),
                data=result.get("data")
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @app.put("/api/v1/testcases/{uuid}", response_model=APIResponse, tags=["测试用例管理"])
    async def update_testcase_api(uuid: str, request: TestCaseUpdateRequest):
        """更新测试用例"""
        try:
            result = modify_testcase_details(
                uuid=uuid,
                title=request.title,
                priority=request.priority,
                description=request.description,
                requires_self_test=request.requires_self_test
            )
            if result.get("status") == "error":
                raise HTTPException(status_code=404, detail=result.get("message"))
            return APIResponse(
                status=result.get("status", "success"),
                message=result.get("message", "更新成功"),
                data=result.get("data")
            )
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    # ==================== 提示词接口 ====================

    @app.post("/api/v1/prompts/testcase-conversion", response_model=PromptResponse, tags=["提示词"])
    async def generate_prompt_api(request: PromptRequest):
        """生成测试用例转换提示词"""
        try:
            prompt = generate_testcase_conversion_prompt(request.document_content)
            return PromptResponse(
                status="success",
                message="提示词生成成功",
                data={"prompt": prompt}
            )
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    return app
