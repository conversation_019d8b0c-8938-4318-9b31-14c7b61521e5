# 测试用例管理数据库功能

本项目新增了基于MySQL的测试用例管理功能，使用SQLAlchemy ORM框架。

## 数据库配置

数据库连接信息：
- 主机: **************:3566
- 用户名: root
- 密码: immotorsAI123456
- 数据库: immotors_ai_dev

## 数据表结构

### 1. 测试用例集表 (test_case_sets)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| uuid | CHAR(36) | 主键，UUID格式 |
| name | VARCHAR(255) | 测试用例集名称 |
| prd_doc_link | TEXT | 原始PRD文档链接 |
| prd_doc_title | VARCHAR(500) | PRD文档标题 |
| author | VARCHAR(100) | 作者 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

### 2. 测试用例表 (test_cases)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| uuid | CHAR(36) | 主键，UUID格式 |
| test_case_set_uuid | CHAR(36) | 外键，关联测试用例集 |
| title | VARCHAR(500) | 测试用例标题 |
| priority | ENUM('P0','P1','P2') | 优先级 |
| requires_self_test | BOOLEAN | 是否需要自测 |
| description | TEXT | 测试用例描述 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

## 业务规则

1. **优先级规则**: P0优先级的测试用例必须设置为需要自测
2. **级联删除**: 删除测试用例集时，会自动删除其下的所有测试用例
3. **UUID主键**: 所有表都使用UUID作为主键，便于分布式环境使用

## 安装依赖

```bash
pip install sqlalchemy pymysql
```

## 初始化数据库

```bash
python3 init_db.py
```

## 测试数据库功能

```bash
python3 test_database.py
python3 test_direct_functions.py
```

## MCP工具函数

### 1. create_testcase_set
创建新的测试用例集 - 用于组织和管理一组相关的测试用例

**功能说明:**
- 在数据库中创建一个新的测试用例集记录
- 自动生成UUID作为唯一标识符
- 记录创建时间和更新时间

**参数:**
- name: 测试用例集名称（必填，如"用户登录功能测试用例集"）
- prd_doc_link: 原始PRD文档链接（必填）
- prd_doc_title: PRD文档标题（可选）
- author: 作者姓名（可选）

### 2. add_testcase_to_set
向指定的测试用例集中添加新的测试用例

**功能说明:**
- 在指定的测试用例集下创建一个新的测试用例
- 自动根据优先级设置是否需要自测（P0必须自测）
- 自动生成UUID和时间戳

**参数:**
- test_case_set_uuid: 目标测试用例集的UUID（必填，36位字符串）
- title: 测试用例标题（必填）
- priority: 优先级（必填，只能是"P0"、"P1"或"P2"）
- description: 测试用例详细描述（可选）

### 3. query_testcase_set_info
查询指定测试用例集的详细信息

**功能说明:**
- 根据UUID查询测试用例集的基本信息
- 返回名称、PRD文档链接、作者、创建时间等信息
- 不包含该用例集下的具体测试用例列表

**参数:**
- uuid: 测试用例集的UUID（必填，36位字符串）

### 4. list_testcases_in_set
获取指定测试用例集下的所有测试用例列表

**功能说明:**
- 查询指定测试用例集下的所有测试用例
- 返回每个测试用例的详细信息（标题、优先级、是否需要自测等）
- 按创建时间排序

**参数:**
- test_case_set_uuid: 测试用例集的UUID（必填，36位字符串）

### 5. list_all_testcase_sets
分页查询所有测试用例集的列表

**功能说明:**
- 获取系统中所有测试用例集的基本信息
- 支持分页查询，避免一次性返回过多数据
- 按创建时间倒序排列（最新的在前）

**参数:**
- limit: 每页返回的最大记录数（默认100，建议不超过500）
- offset: 跳过的记录数，用于分页（默认0）

### 6. modify_testcase_details
修改指定测试用例的详细信息

**功能说明:**
- 更新已存在的测试用例的各项属性
- 支持部分字段更新，只传入需要修改的字段即可
- 自动更新修改时间戳
- 如果修改优先级为P0，会自动设置需要自测为true

**参数:**
- uuid: 要修改的测试用例UUID（必填，36位字符串）
- title: 新的测试用例标题（可选）
- priority: 新的优先级（可选，只能是"P0"、"P1"或"P2"）
- description: 新的测试用例描述（可选）
- requires_self_test: 是否需要自测（可选，布尔值）

## MCP提示词

### 7. build_testcase_conversion_prompt
构建用于将PRD文档转换为测试用例表格的完整提示词

**功能说明:**
- 生成包含详细转换规则的AI提示词
- 提示词包含格式要求、示例和具体的文档内容
- 返回可直接发送给AI助手的完整提示词
- 不执行实际转换，仅生成提示词供AI处理

**参数:**
- document_content: 需要转换的PRD文档内容

## MCP提示词

### generate_testcase_conversion_prompt
生成将PRD文档转换为标准测试用例表格的AI提示词

**功能说明:**
- 创建结构化的提示词，指导AI将PRD文档内容转换为标准的测试用例表格
- 包含详细的转换规则、格式要求和示例
- 确保输出符合"模块、标题、优先级、需要自测、描述"五列格式
- 自动处理P0优先级必须自测的业务规则

**参数:**
- document_content: 需要转换的PRD文档内容（支持中文，包含功能描述、优先级等信息）

**使用示例:**
```python
# 通过MCP协议调用提示词
document = "用户登录模块，测试手机号格式校验功能..."
# 调用 generate_testcase_conversion_prompt 提示词
# 将返回的提示词发送给AI助手处理
```

## 启动服务

```bash
python3 fastmcp_server.py
```

服务将在 `http://localhost:8001` 启动，支持MCP协议调用。

## 项目完成状态

✅ **已完成的功能:**

1. **数据库表结构设计**
   - 测试用例集表 (test_case_sets)
   - 测试用例表 (test_cases)
   - 支持UUID主键、外键关联、级联删除

2. **ORM模型实现**
   - 使用SQLAlchemy ORM框架
   - 支持MySQL数据库连接
   - 完整的CRUD操作

3. **业务逻辑实现**
   - P0优先级自动设置需要自测
   - 数据验证和错误处理
   - 时间戳自动管理

4. **MCP工具函数**
   - create_test_case_set: 创建测试用例集
   - create_test_case: 创建测试用例
   - get_test_case_set: 查询测试用例集
   - get_test_cases_by_set: 查询用例集下的所有用例
   - list_test_case_sets: 列出所有测试用例集
   - update_test_case: 更新测试用例

5. **MCP提示词**
   - doc_to_testcase_prompt: 文档转测试用例的提示词生成器

6. **测试验证**
   - 数据库初始化脚本
   - 完整的功能测试脚本
   - 直接函数调用测试
   - 提示词功能测试

## 测试结果

所有核心功能已通过测试：

- ✅ 数据库连接和表创建
- ✅ 测试用例集的创建、查询、列表
- ✅ 测试用例的创建、查询、更新
- ✅ P0优先级自动设置需要自测的业务规则
- ✅ UUID主键和外键关联
- ✅ 时间戳自动管理
- ✅ 文档转测试用例的提示词生成

## 使用方式

### 1. 直接使用数据库类
```python
from utils.database import TestCaseStorage, TestCaseManager

# 创建管理器
storage = TestCaseStorage()
manager = TestCaseManager()

# 创建测试用例集
test_set = storage.create_test_case_set(
    name="测试用例集名称",
    prd_doc_link="https://example.com/prd",
    prd_doc_title="PRD标题",
    author="作者"
)

# 创建测试用例
test_case = manager.create_test_case(
    test_case_set_uuid=test_set.uuid,
    title="测试用例标题",
    priority="P0",  # P0/P1/P2
    description="测试用例描述"
)
```

### 2. 通过MCP服务器调用
启动服务器后，可以通过MCP协议调用相应的工具函数。

## 数据库连接信息

- **主机**: **************:3566
- **数据库**: immotors_ai_dev
- **用户**: root
- **密码**: immotorsAI123456

## 文件结构

```
MCP_server/
├── utils/
│   ├── __init__.py
│   └── database.py          # ORM模型和数据库操作类
├── fastmcp_server.py        # MCP服务器和工具函数
├── init_db.py              # 数据库初始化脚本
├── test_database.py        # 数据库功能测试
├── test_direct_functions.py # 直接函数调用测试
├── requirements.txt        # 依赖包列表
└── README_database.md      # 本文档
```
