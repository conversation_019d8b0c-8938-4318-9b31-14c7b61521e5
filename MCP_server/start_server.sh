#!/bin/bash

# MCP服务器启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查Python环境
check_python() {
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装"
        exit 1
    fi
    
    python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    print_info "Python版本: $python_version"
}

# 检查依赖
check_dependencies() {
    print_info "检查依赖包..."
    
    if ! python3 -c "import fastmcp" &> /dev/null; then
        print_error "fastmcp 未安装，请运行: pip install fastmcp"
        exit 1
    fi
    
    if ! python3 -c "import fastapi" &> /dev/null; then
        print_warning "fastapi 未安装，正在安装..."
        pip install fastapi uvicorn
    fi
    
    print_success "依赖检查完成"
}

# 检查端口占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_warning "端口 $port 已被占用"
        return 1
    fi
    return 0
}

# 显示帮助信息
show_help() {
    echo "MCP服务器启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -m, --mode MODE     服务器模式: hybrid|mcp|http (默认: hybrid)"
    echo "  -p, --mcp-port PORT MCP服务器端口 (默认: 8000)"
    echo "  -h, --http-port PORT HTTP服务器端口 (默认: 8001)"
    echo "  --help              显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                          # 启动混合模式服务器"
    echo "  $0 -m mcp                   # 仅启动MCP服务器"
    echo "  $0 -m http                  # 仅启动HTTP服务器"
    echo "  $0 -m hybrid -p 9000 -h 9001  # 自定义端口"
}

# 默认参数
MODE="hybrid"
MCP_PORT=8000
HTTP_PORT=8001

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -m|--mode)
            MODE="$2"
            shift 2
            ;;
        -p|--mcp-port)
            MCP_PORT="$2"
            shift 2
            ;;
        -h|--http-port)
            HTTP_PORT="$2"
            shift 2
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证模式参数
if [[ ! "$MODE" =~ ^(hybrid|mcp|http)$ ]]; then
    print_error "无效的模式: $MODE"
    print_info "支持的模式: hybrid, mcp, http"
    exit 1
fi

# 主函数
main() {
    echo "🚀 MCP服务器启动脚本"
    echo "===================="
    
    # 检查环境
    check_python
    check_dependencies
    
    # 检查端口
    if [[ "$MODE" == "hybrid" || "$MODE" == "mcp" ]]; then
        if ! check_port $MCP_PORT; then
            print_error "MCP端口 $MCP_PORT 被占用，请选择其他端口或停止占用进程"
            exit 1
        fi
    fi
    
    if [[ "$MODE" == "hybrid" || "$MODE" == "http" ]]; then
        if ! check_port $HTTP_PORT; then
            print_error "HTTP端口 $HTTP_PORT 被占用，请选择其他端口或停止占用进程"
            exit 1
        fi
    fi
    
    # 显示启动信息
    print_info "启动模式: $MODE"
    if [[ "$MODE" == "hybrid" || "$MODE" == "mcp" ]]; then
        print_info "MCP服务器端口: $MCP_PORT"
    fi
    if [[ "$MODE" == "hybrid" || "$MODE" == "http" ]]; then
        print_info "HTTP服务器端口: $HTTP_PORT"
        print_info "API文档地址: http://localhost:$HTTP_PORT/docs"
    fi
    
    echo ""
    print_success "正在启动服务器..."
    
    # 启动服务器
    python3 hybrid_server.py \
        --mode "$MODE" \
        --mcp-port "$MCP_PORT" \
        --http-port "$HTTP_PORT"
}

# 信号处理
trap 'print_info "正在停止服务器..."; exit 0' INT TERM

# 运行主函数
main "$@"
