"""
HTTP API接口测试脚本
测试所有HTTP API接口的功能
"""

import requests
import json
import time
import uuid
from typing import Dict, Any


class HTTPAPITester:
    """HTTP API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.test_results = []
        
        # 设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def log_test(self, test_name: str, success: bool, message: str = "", data: Any = None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": time.time(),
            "data": data
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
        if data and not success:
            print(f"   详细信息: {data}")
    
    def test_health_check(self):
        """测试健康检查接口"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    self.log_test("健康检查", True, "服务器运行正常")
                    return True
                else:
                    self.log_test("健康检查", False, "响应格式错误", data)
            else:
                self.log_test("健康检查", False, f"HTTP状态码: {response.status_code}")
        except Exception as e:
            self.log_test("健康检查", False, f"请求失败: {str(e)}")
        return False
    
    def test_math_operations(self):
        """测试数学运算接口"""
        test_cases = [
            ("加法", "/api/v1/math/add", {"a": 10, "b": 5}, 15),
            ("减法", "/api/v1/math/subtract", {"a": 10, "b": 3}, 7),
            ("乘法", "/api/v1/math/multiply", {"a": 4, "b": 6}, 24),
            ("除法", "/api/v1/math/divide", {"a": 20, "b": 4}, 5),
        ]
        
        for test_name, endpoint, payload, expected in test_cases:
            try:
                response = self.session.post(f"{self.base_url}{endpoint}", json=payload)
                if response.status_code == 200:
                    data = response.json()
                    if data.get("status") == "success" and data.get("data") == expected:
                        self.log_test(f"数学运算-{test_name}", True, f"结果正确: {expected}")
                    else:
                        self.log_test(f"数学运算-{test_name}", False, "结果错误", data)
                else:
                    self.log_test(f"数学运算-{test_name}", False, f"HTTP状态码: {response.status_code}")
            except Exception as e:
                self.log_test(f"数学运算-{test_name}", False, f"请求失败: {str(e)}")
        
        # 测试除零错误
        try:
            response = self.session.post(f"{self.base_url}/api/v1/math/divide", json={"a": 10, "b": 0})
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "error":
                    self.log_test("数学运算-除零处理", True, "正确处理除零错误")
                else:
                    self.log_test("数学运算-除零处理", False, "未正确处理除零错误", data)
            else:
                self.log_test("数学运算-除零处理", False, f"HTTP状态码: {response.status_code}")
        except Exception as e:
            self.log_test("数学运算-除零处理", False, f"请求失败: {str(e)}")
    
    def test_testcase_management(self):
        """测试测试用例管理接口"""
        # 1. 创建测试用例集
        testcase_set_data = {
            "name": f"HTTP API测试用例集-{int(time.time())}",
            "prd_doc_link": "https://example.com/test-prd",
            "prd_doc_title": "HTTP API测试PRD",
            "author": "API测试器"
        }
        
        testcase_set_uuid = None
        try:
            response = self.session.post(f"{self.base_url}/api/v1/testcase-sets", json=testcase_set_data)
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success" and data.get("data", {}).get("uuid"):
                    testcase_set_uuid = data["data"]["uuid"]
                    self.log_test("创建测试用例集", True, f"UUID: {testcase_set_uuid}")
                else:
                    self.log_test("创建测试用例集", False, "响应格式错误", data)
                    return
            else:
                self.log_test("创建测试用例集", False, f"HTTP状态码: {response.status_code}")
                return
        except Exception as e:
            self.log_test("创建测试用例集", False, f"请求失败: {str(e)}")
            return
        
        # 2. 查询测试用例集信息
        try:
            response = self.session.get(f"{self.base_url}/api/v1/testcase-sets/{testcase_set_uuid}")
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success" and data.get("data", {}).get("name") == testcase_set_data["name"]:
                    self.log_test("查询测试用例集", True, "信息正确")
                else:
                    self.log_test("查询测试用例集", False, "信息不匹配", data)
            else:
                self.log_test("查询测试用例集", False, f"HTTP状态码: {response.status_code}")
        except Exception as e:
            self.log_test("查询测试用例集", False, f"请求失败: {str(e)}")
        
        # 3. 创建测试用例
        testcase_data = {
            "test_case_set_uuid": testcase_set_uuid,
            "title": "HTTP API测试用例",
            "priority": "P1",
            "description": "测试HTTP API接口功能"
        }
        
        testcase_uuid = None
        try:
            response = self.session.post(f"{self.base_url}/api/v1/testcases", json=testcase_data)
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success" and data.get("data", {}).get("uuid"):
                    testcase_uuid = data["data"]["uuid"]
                    self.log_test("创建测试用例", True, f"UUID: {testcase_uuid}")
                else:
                    self.log_test("创建测试用例", False, "响应格式错误", data)
            else:
                self.log_test("创建测试用例", False, f"HTTP状态码: {response.status_code}")
        except Exception as e:
            self.log_test("创建测试用例", False, f"请求失败: {str(e)}")
        
        # 4. 查询测试用例列表
        try:
            response = self.session.get(f"{self.base_url}/api/v1/testcase-sets/{testcase_set_uuid}/testcases")
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success" and len(data.get("data", [])) > 0:
                    self.log_test("查询测试用例列表", True, f"找到 {len(data['data'])} 个测试用例")
                else:
                    self.log_test("查询测试用例列表", False, "未找到测试用例", data)
            else:
                self.log_test("查询测试用例列表", False, f"HTTP状态码: {response.status_code}")
        except Exception as e:
            self.log_test("查询测试用例列表", False, f"请求失败: {str(e)}")
        
        # 5. 更新测试用例
        if testcase_uuid:
            update_data = {
                "title": "更新后的HTTP API测试用例",
                "priority": "P0",
                "description": "更新后的测试用例描述"
            }
            try:
                response = self.session.put(f"{self.base_url}/api/v1/testcases/{testcase_uuid}", json=update_data)
                if response.status_code == 200:
                    data = response.json()
                    if data.get("status") == "success":
                        self.log_test("更新测试用例", True, "更新成功")
                    else:
                        self.log_test("更新测试用例", False, "更新失败", data)
                else:
                    self.log_test("更新测试用例", False, f"HTTP状态码: {response.status_code}")
            except Exception as e:
                self.log_test("更新测试用例", False, f"请求失败: {str(e)}")
    
    def test_prompt_generation(self):
        """测试提示词生成接口"""
        prompt_data = {
            "document_content": "用户登录模块，测试手机号格式校验功能。优先级为P0，需开发人员自测。"
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/v1/prompts/testcase-conversion", json=prompt_data)
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success" and data.get("data", {}).get("prompt"):
                    self.log_test("提示词生成", True, "提示词生成成功")
                else:
                    self.log_test("提示词生成", False, "响应格式错误", data)
            else:
                self.log_test("提示词生成", False, f"HTTP状态码: {response.status_code}")
        except Exception as e:
            self.log_test("提示词生成", False, f"请求失败: {str(e)}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("🧪 开始HTTP API接口测试")
        print("=" * 60)
        
        # 检查服务器是否可用
        if not self.test_health_check():
            print("❌ 服务器不可用，停止测试")
            return
        
        print("\n📊 测试数学运算接口...")
        self.test_math_operations()
        
        print("\n📋 测试测试用例管理接口...")
        self.test_testcase_management()
        
        print("\n💬 测试提示词生成接口...")
        self.test_prompt_generation()
        
        # 统计测试结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 60)
        print("📊 测试结果统计")
        print("=" * 60)
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   - {result['test_name']}: {result['message']}")
        
        return passed_tests == total_tests


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="HTTP API接口测试")
    parser.add_argument("--url", default="http://localhost:8001", help="API服务器地址")
    
    args = parser.parse_args()
    
    tester = HTTPAPITester(args.url)
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过!")
        exit(0)
    else:
        print("\n💥 部分测试失败!")
        exit(1)


if __name__ == "__main__":
    main()
