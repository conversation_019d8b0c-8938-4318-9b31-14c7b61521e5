# MCP Server HTTP API 文档

本项目现在支持两种协议：
1. **MCP协议** - 原有的Model Context Protocol，支持SSE、stdio、streamable-http
2. **HTTP REST API** - 新增的RESTful API接口，方便Web应用和其他系统集成

## 🚀 快速启动

### 方式一：混合模式（推荐）
同时启动MCP和HTTP服务器：
```bash
python3 hybrid_server.py --mode hybrid
```

### 方式二：单独启动
```bash
# 仅启动MCP服务器（端口8000）
python3 hybrid_server.py --mode mcp

# 仅启动HTTP API服务器（端口8001）
python3 hybrid_server.py --mode http

# 或者使用原有方式
python3 fastmcp_server.py --http
```

### 方式三：自定义端口
```bash
python3 hybrid_server.py --mode hybrid --mcp-port 9000 --http-port 9001
```

## 📡 服务器信息

### 默认端口配置
- **MCP服务器**: http://localhost:8000
- **HTTP API服务器**: http://localhost:8001
- **API文档**: http://localhost:8001/docs
- **ReDoc文档**: http://localhost:8001/redoc

### 支持的协议
- **MCP协议**: SSE, stdio, streamable-http
- **HTTP协议**: RESTful API with JSON

## 📚 API接口文档

### 1. 健康检查

**GET** `/health`

检查服务器运行状态。

**响应示例:**
```json
{
  "status": "success",
  "message": "MCP Server HTTP API is running",
  "data": {
    "timestamp": **********.789
  }
}
```

### 2. 数学运算接口

#### 加法运算
**POST** `/api/v1/math/add`

```json
{
  "a": 10,
  "b": 5
}
```

#### 减法运算
**POST** `/api/v1/math/subtract`

#### 乘法运算
**POST** `/api/v1/math/multiply`

#### 除法运算
**POST** `/api/v1/math/divide`

**响应示例:**
```json
{
  "status": "success",
  "message": "加法运算成功",
  "data": 15
}
```

### 3. 测试用例集管理

#### 创建测试用例集
**POST** `/api/v1/testcase-sets`

```json
{
  "name": "用户登录功能测试用例集",
  "prd_doc_link": "https://example.feishu.cn/docx/abc123",
  "prd_doc_title": "用户登录功能PRD文档",
  "author": "张三"
}
```

#### 查询测试用例集
**GET** `/api/v1/testcase-sets/{uuid}`

#### 列出所有测试用例集
**GET** `/api/v1/testcase-sets?limit=100&offset=0`

**响应示例:**
```json
{
  "status": "success",
  "message": "测试用例集创建成功",
  "data": {
    "uuid": "550e8400-e29b-41d4-a716-************",
    "name": "用户登录功能测试用例集",
    "prd_doc_link": "https://example.feishu.cn/docx/abc123",
    "prd_doc_title": "用户登录功能PRD文档",
    "author": "张三",
    "created_at": "2023-12-21T10:30:00",
    "updated_at": "2023-12-21T10:30:00"
  }
}
```

### 4. 测试用例管理

#### 创建测试用例
**POST** `/api/v1/testcases`

```json
{
  "test_case_set_uuid": "550e8400-e29b-41d4-a716-************",
  "title": "用户正常登录功能验证",
  "priority": "P0",
  "description": "验证用户使用正确的用户名和密码能够成功登录系统"
}
```

#### 查询测试用例列表
**GET** `/api/v1/testcase-sets/{set_uuid}/testcases`

#### 更新测试用例
**PUT** `/api/v1/testcases/{uuid}`

```json
{
  "title": "更新后的测试用例标题",
  "priority": "P1",
  "description": "更新后的描述",
  "requires_self_test": true
}
```

### 5. 文档处理

#### 获取飞书文档
**POST** `/api/v1/docs/feishu`

```json
{
  "doc_link": "https://example.feishu.cn/docx/abc123",
  "lang": 0
}
```

#### Markdown转JSON
**POST** `/api/v1/docs/markdown-to-json`

```json
{
  "markdown_content": "| 列1 | 列2 |\n|-----|-----|\n| 值1 | 值2 |"
}
```

#### JSON转Markdown
**POST** `/api/v1/docs/json-to-markdown`

```json
{
  "json_data": "[{\"列1\": \"值1\", \"列2\": \"值2\"}]"
}
```

### 6. 提示词生成

#### 生成测试用例转换提示词
**POST** `/api/v1/prompts/testcase-conversion`

```json
{
  "document_content": "用户登录模块，测试手机号格式校验功能。优先级为P0，需开发人员自测。"
}
```

## 🧪 测试

### 运行API测试
```bash
# 确保HTTP服务器正在运行
python3 test_http_api.py

# 指定不同的服务器地址
python3 test_http_api.py --url http://localhost:9001
```

### 使用curl测试
```bash
# 健康检查
curl http://localhost:8001/health

# 数学运算
curl -X POST http://localhost:8001/api/v1/math/add \
  -H "Content-Type: application/json" \
  -d '{"a": 10, "b": 5}'

# 创建测试用例集
curl -X POST http://localhost:8001/api/v1/testcase-sets \
  -H "Content-Type: application/json" \
  -d '{
    "name": "API测试用例集",
    "prd_doc_link": "https://example.com/prd",
    "author": "测试用户"
  }'
```

## 🔧 配置

### 环境变量
```bash
# 数据库配置（如果需要）
export DB_HOST=**************
export DB_PORT=3566
export DB_USER=root
export DB_PASSWORD=immotorsAI123456
export DB_NAME=immotors_ai_dev
```

### 依赖安装
```bash
pip install -r requirements.txt
```

新增的依赖：
- `fastapi` - Web框架
- `pydantic` - 数据验证

## 🛡️ 安全特性

- **CORS支持** - 跨域资源共享
- **请求日志** - 详细的请求/响应日志
- **错误处理** - 统一的错误响应格式
- **参数验证** - 自动的请求参数验证
- **安全头** - 添加安全相关的HTTP头

## 📊 监控和日志

### 请求日志格式
```
[HTTP-a1b2c3d4] --> POST /api/v1/testcase-sets
[HTTP-a1b2c3d4] Client: 127.0.0.1:54321
[HTTP-a1b2c3d4] Body: {"name": "测试用例集", ...}
[HTTP-a1b2c3d4] <-- 200 (0.123s)
```

### 响应格式
所有API响应都遵循统一格式：
```json
{
  "status": "success|error",
  "message": "操作结果描述",
  "data": "实际数据或null"
}
```

## 🔄 从MCP到HTTP的迁移

如果你之前使用MCP协议，现在可以轻松迁移到HTTP API：

### MCP调用示例
```python
# MCP协议调用
result = mcp_client.call_tool("create_testcase_set", {
    "name": "测试用例集",
    "prd_doc_link": "https://example.com/prd"
})
```

### HTTP API调用示例
```python
# HTTP API调用
import requests

response = requests.post("http://localhost:8001/api/v1/testcase-sets", json={
    "name": "测试用例集",
    "prd_doc_link": "https://example.com/prd"
})
result = response.json()
```

## 🚀 部署

### Docker部署
```bash
# 构建镜像
docker build -t mcp-server .

# 运行容器（混合模式）
docker run -p 8000:8000 -p 8001:8001 mcp-server python3 hybrid_server.py

# 仅HTTP模式
docker run -p 8001:8001 mcp-server python3 hybrid_server.py --mode http
```

### Docker Compose
```yaml
version: '3.8'
services:
  mcp-server:
    build: .
    ports:
      - "8000:8000"  # MCP端口
      - "8001:8001"  # HTTP API端口
    command: python3 hybrid_server.py --mode hybrid
```

## 📞 支持

如果遇到问题：
1. 查看服务器日志
2. 访问API文档：http://localhost:8001/docs
3. 运行测试脚本：`python3 test_http_api.py`
4. 检查端口是否被占用

## 🎯 下一步计划

- [ ] 添加认证和授权
- [ ] 实现API限流
- [ ] 添加更多文档处理功能
- [ ] 支持WebSocket实时通信
- [ ] 添加API版本管理
