"""
MCP工具注册器 - 统一管理所有MCP工具函数
"""

from fastmcp import FastMCP
from .testcase_management import (
    create_testcase_set,
    query_testcase_set_info,
    list_all_testcase_sets,
    add_testcase_to_set,
    list_testcases_in_set,
    modify_testcase_details
)
from .prompt_tools import (
    generate_testcase_conversion_prompt
)


def register_all_tools(mcp: FastMCP):
    """
    注册所有MCP工具函数和提示词
    
    参数:
        mcp: FastMCP实例
    """
    
    # 注册测试用例集相关工具
    mcp.tool()(create_testcase_set)
    mcp.tool()(query_testcase_set_info)
    mcp.tool()(list_all_testcase_sets)
    
    # 注册测试用例相关工具
    mcp.tool()(add_testcase_to_set)
    mcp.tool()(list_testcases_in_set)
    mcp.tool()(modify_testcase_details)
    
    # 注册提示词相关工具
    mcp.prompt()(generate_testcase_conversion_prompt)
    
    print("✅ 所有MCP工具函数已注册完成")
    print("📋 已注册的工具函数:")
    print("  测试用例集工具:")
    print("    - create_testcase_set: 创建新的测试用例集")
    print("    - query_testcase_set_info: 查询测试用例集详细信息")
    print("    - list_all_testcase_sets: 分页查询所有测试用例集")
    print("  测试用例工具:")
    print("    - add_testcase_to_set: 向用例集添加测试用例")
    print("    - list_testcases_in_set: 获取用例集下的所有测试用例")
    print("    - modify_testcase_details: 修改测试用例详细信息")
    print("  提示词工具:")
    print("    - generate_testcase_conversion_prompt: 生成测试用例转换提示词")



