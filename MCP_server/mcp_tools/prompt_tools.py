"""
提示词相关的MCP工具函数
"""

from fastmcp.server.context import Context



def generate_testcase_conversion_prompt(document_content: str) -> str:
    """
    生成将PRD文档转换为标准测试用例表格的AI提示词
    
    功能说明：
    - 创建结构化的提示词，指导AI将PRD文档内容转换为标准的测试用例表格
    - 包含详细的转换规则、格式要求和示例
    - 确保输出符合"模块、标题、优先级、需要自测、描述"五列格式
    - 自动处理P0优先级必须自测的业务规则
    
    参数:
        document_content: 需要转换的PRD文档内容（支持中文，包含功能描述、优先级等信息）
    
    返回:
        完整的AI提示词字符串，可直接发送给AI助手进行处理
    """
    return f"""请根据用户提供的文档内容，将其转换为符合指定格式的 Markdown 表格。表格必须包含以下五列：模块、标题、优先级、需要自测、描述。每一行对应一条功能点或测试点信息，确保信息准确无误地从输入文档中提取并填入对应列中。

具体操作步骤如下：
1. 仔细阅读输入文档，识别出每一个功能模块及其相关属性，包括标题、优先级（如 P0、P1 、P2等）、是否需要自测（是/否，其中P0必须为是），以及描述信息。
2. 提取"前置条件"和"操作步骤"等内容，并将其整合到"描述"列中，保持语义完整、通顺。
3. 确保所有列名与要求一致，使用竖线分隔，表头与内容之间用分隔线连接。
4. 输出必须为标准的 Markdown 表格格式，不得包含任何 XML 标签或其他标记语言。
5. 若输入文档中包含多个测试点，请在表格中分别列出多行，每行对应一个独立的测试点。
6. 保持语言为中文，标点符号使用中文全角符号，确保格式整洁统一。
7. 输出内容仅包含 Markdown 表格，不得附加任何解释、说明或额外文本。

示例：
输入文档：
用户登录模块，测试手机号格式校验功能。优先级为P0，需开发人员自测。前置条件：用户进入登录页面。操作步骤：输入不符合格式的手机号，如12345678901。

输出：
| 模块 | 标题 | 优先级 | 需要自测 | 描述 |
|----|------|--------|--------|------|
| 用户登录 | 手机号格式校验 | P0 | 是 | 前置条件：用户进入登录页面。操作步骤：输入不符合格式的手机号，如12345678901。|

现在请处理以下文档内容：

{document_content}"""
