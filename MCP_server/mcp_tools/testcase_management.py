"""
测试用例和测试用例集管理的MCP工具函数
"""

from fastmcp.server.context import Context
from utils.database import TestCaseStorage, TestCaseManager


# ==================== 测试用例集相关函数 ====================

def create_testcase_set(name: str, prd_doc_link: str, prd_doc_title: str = None, author: str = None, ctx: Context = None) -> dict:
    """
    创建新的测试用例集 - 用于组织和管理一组相关的测试用例
    
    功能说明：
    - 在数据库中创建一个新的测试用例集记录
    - 自动生成UUID作为唯一标识符
    - 记录创建时间和更新时间
    
    参数:
        name: 测试用例集名称（必填，如"用户登录功能测试用例集"）
        prd_doc_link: 原始PRD文档链接（必填，如"https://example.feishu.cn/docx/abc123"）
        prd_doc_title: PRD文档标题（可选，如"用户登录功能PRD文档"）
        author: 作者姓名（可选，如"张三"）
    
    返回:
        包含创建结果的字典，成功时包含新创建的测试用例集信息
    """
    if ctx:
        print(f"[create_testcase_set] request_id={ctx.request_id}, client_id={ctx.client_id}")
    
    print(f"[create_testcase_set] Creating test case set: {name}")
    
    try:
        storage = TestCaseStorage()
        test_case_set = storage.create_test_case_set(
            name=name,
            prd_doc_link=prd_doc_link,
            prd_doc_title=prd_doc_title,
            author=author
        )
        
        result = {
            "status": "success",
            "message": "测试用例集创建成功",
            "data": {
                "uuid": test_case_set.uuid,
                "name": test_case_set.name,
                "prd_doc_link": test_case_set.prd_doc_link,
                "prd_doc_title": test_case_set.prd_doc_title,
                "author": test_case_set.author,
                "created_at": test_case_set.created_at.isoformat() if test_case_set.created_at else None,
                "updated_at": test_case_set.updated_at.isoformat() if test_case_set.updated_at else None
            }
        }
        
        print(f"[create_testcase_set] Successfully created test case set with UUID: {test_case_set.uuid}")
        return result
        
    except Exception as e:
        error_result = {
            "status": "error",
            "message": f"创建测试用例集失败: {str(e)}",
            "data": None
        }
        print(f"[create_testcase_set] Error: {str(e)}")
        return error_result


def query_testcase_set_info(uuid: str, ctx: Context = None) -> dict:
    """
    查询指定测试用例集的详细信息
    
    功能说明：
    - 根据UUID查询测试用例集的基本信息
    - 返回名称、PRD文档链接、作者、创建时间等信息
    - 不包含该用例集下的具体测试用例列表
    
    参数:
        uuid: 测试用例集的UUID（必填，36位字符串）
    
    返回:
        包含测试用例集详细信息的字典
    """
    if ctx:
        print(f"[query_testcase_set_info] request_id={ctx.request_id}, client_id={ctx.client_id}")
    
    print(f"[query_testcase_set_info] Getting test case set: {uuid}")
    
    try:
        storage = TestCaseStorage()
        test_case_set = storage.get_test_case_set(uuid)
        
        if not test_case_set:
            return {
                "status": "error",
                "message": "测试用例集不存在",
                "data": None
            }
        
        result = {
            "status": "success",
            "message": "获取测试用例集成功",
            "data": {
                "uuid": test_case_set.uuid,
                "name": test_case_set.name,
                "prd_doc_link": test_case_set.prd_doc_link,
                "prd_doc_title": test_case_set.prd_doc_title,
                "author": test_case_set.author,
                "created_at": test_case_set.created_at.isoformat() if test_case_set.created_at else None,
                "updated_at": test_case_set.updated_at.isoformat() if test_case_set.updated_at else None
            }
        }
        
        print(f"[query_testcase_set_info] Successfully retrieved test case set: {test_case_set.name}")
        return result
        
    except Exception as e:
        error_result = {
            "status": "error",
            "message": f"获取测试用例集失败: {str(e)}",
            "data": None
        }
        print(f"[query_testcase_set_info] Error: {str(e)}")
        return error_result


def list_all_testcase_sets(limit: int = 100, offset: int = 0, ctx: Context = None) -> dict:
    """
    分页查询所有测试用例集的列表
    
    功能说明：
    - 获取系统中所有测试用例集的基本信息
    - 支持分页查询，避免一次性返回过多数据
    - 按创建时间倒序排列（最新的在前）
    
    参数:
        limit: 每页返回的最大记录数（默认100，建议不超过500）
        offset: 跳过的记录数，用于分页（默认0，表示从第一条开始）
    
    返回:
        包含测试用例集列表的字典，包括总数量和分页信息
    """
    if ctx:
        print(f"[list_all_testcase_sets] request_id={ctx.request_id}, client_id={ctx.client_id}")
    
    print(f"[list_all_testcase_sets] Listing test case sets with limit={limit}, offset={offset}")
    
    try:
        storage = TestCaseStorage()
        test_case_sets = storage.list_test_case_sets(limit=limit, offset=offset)
        
        sets_data = []
        for test_set in test_case_sets:
            sets_data.append({
                "uuid": test_set.uuid,
                "name": test_set.name,
                "prd_doc_link": test_set.prd_doc_link,
                "prd_doc_title": test_set.prd_doc_title,
                "author": test_set.author,
                "created_at": test_set.created_at.isoformat() if test_set.created_at else None,
                "updated_at": test_set.updated_at.isoformat() if test_set.updated_at else None
            })
        
        result = {
            "status": "success",
            "message": f"获取测试用例集列表成功，共{len(sets_data)}条",
            "data": sets_data,
            "count": len(sets_data),
            "limit": limit,
            "offset": offset
        }
        
        print(f"[list_all_testcase_sets] Successfully retrieved {len(sets_data)} test case sets")
        return result
        
    except Exception as e:
        error_result = {
            "status": "error",
            "message": f"获取测试用例集列表失败: {str(e)}",
            "data": None,
            "count": 0,
            "limit": limit,
            "offset": offset
        }
        print(f"[list_all_testcase_sets] Error: {str(e)}")
        return error_result


# ==================== 测试用例相关函数 ====================

def add_testcase_to_set(test_case_set_uuid: str, title: str, priority: str, description: str = None, ctx: Context = None) -> dict:
    """
    向指定的测试用例集中添加新的测试用例
    
    功能说明：
    - 在指定的测试用例集下创建一个新的测试用例
    - 自动根据优先级设置是否需要自测（P0必须自测）
    - 自动生成UUID和时间戳
    
    参数:
        test_case_set_uuid: 目标测试用例集的UUID（必填，36位字符串）
        title: 测试用例标题（必填，如"用户正常登录功能验证"）
        priority: 优先级（必填，只能是"P0"、"P1"或"P2"）
        description: 测试用例详细描述（可选，包含前置条件、操作步骤等）
    
    返回:
        包含创建结果的字典，成功时包含新创建的测试用例信息
    """
    if ctx:
        print(f"[add_testcase_to_set] request_id={ctx.request_id}, client_id={ctx.client_id}")
    
    print(f"[add_testcase_to_set] Creating test case: {title} with priority {priority}")
    
    try:
        # 验证优先级
        if priority not in ["P0", "P1", "P2"]:
            raise ValueError("优先级必须是 P0、P1 或 P2")
        
        manager = TestCaseManager()
        test_case = manager.create_test_case(
            test_case_set_uuid=test_case_set_uuid,
            title=title,
            priority=priority,
            description=description
        )
        
        result = {
            "status": "success",
            "message": "测试用例创建成功",
            "data": {
                "uuid": test_case.uuid,
                "test_case_set_uuid": test_case.test_case_set_uuid,
                "title": test_case.title,
                "priority": test_case.priority.value,
                "requires_self_test": test_case.requires_self_test,
                "description": test_case.description,
                "created_at": test_case.created_at.isoformat() if test_case.created_at else None,
                "updated_at": test_case.updated_at.isoformat() if test_case.updated_at else None
            }
        }
        
        print(f"[add_testcase_to_set] Successfully created test case with UUID: {test_case.uuid}")
        return result
        
    except Exception as e:
        error_result = {
            "status": "error",
            "message": f"创建测试用例失败: {str(e)}",
            "data": None
        }
        print(f"[add_testcase_to_set] Error: {str(e)}")
        return error_result


def list_testcases_in_set(test_case_set_uuid: str, ctx: Context = None) -> dict:
    """
    获取指定测试用例集下的所有测试用例列表

    功能说明：
    - 查询指定测试用例集下的所有测试用例
    - 返回每个测试用例的详细信息（标题、优先级、是否需要自测等）
    - 按创建时间排序

    参数:
        test_case_set_uuid: 测试用例集的UUID（必填，36位字符串）

    返回:
        包含测试用例列表的字典，包括用例数量统计
    """
    if ctx:
        print(f"[list_testcases_in_set] request_id={ctx.request_id}, client_id={ctx.client_id}")

    print(f"[list_testcases_in_set] Getting test cases for set: {test_case_set_uuid}")

    try:
        manager = TestCaseManager()
        test_cases = manager.get_test_cases_by_set(test_case_set_uuid)

        cases_data = []
        for case in test_cases:
            cases_data.append({
                "uuid": case.uuid,
                "test_case_set_uuid": case.test_case_set_uuid,
                "title": case.title,
                "priority": case.priority.value,
                "requires_self_test": case.requires_self_test,
                "description": case.description,
                "created_at": case.created_at.isoformat() if case.created_at else None,
                "updated_at": case.updated_at.isoformat() if case.updated_at else None
            })

        result = {
            "status": "success",
            "message": f"获取测试用例成功，共{len(cases_data)}条",
            "data": cases_data,
            "count": len(cases_data)
        }

        print(f"[list_testcases_in_set] Successfully retrieved {len(cases_data)} test cases")
        return result

    except Exception as e:
        error_result = {
            "status": "error",
            "message": f"获取测试用例失败: {str(e)}",
            "data": None,
            "count": 0
        }
        print(f"[list_testcases_in_set] Error: {str(e)}")
        return error_result


def modify_testcase_details(uuid: str, title: str = None, priority: str = None, description: str = None, requires_self_test: bool = None, ctx: Context = None) -> dict:
    """
    修改指定测试用例的详细信息

    功能说明：
    - 更新已存在的测试用例的各项属性
    - 支持部分字段更新，只传入需要修改的字段即可
    - 自动更新修改时间戳
    - 如果修改优先级为P0，会自动设置需要自测为true

    参数:
        uuid: 要修改的测试用例UUID（必填，36位字符串）
        title: 新的测试用例标题（可选）
        priority: 新的优先级（可选，只能是"P0"、"P1"或"P2"）
        description: 新的测试用例描述（可选）
        requires_self_test: 是否需要自测（可选，布尔值）

    返回:
        包含更新结果的字典，成功时包含更新后的测试用例信息
    """
    if ctx:
        print(f"[modify_testcase_details] request_id={ctx.request_id}, client_id={ctx.client_id}")

    print(f"[modify_testcase_details] Updating test case: {uuid}")

    try:
        # 验证优先级
        if priority and priority not in ["P0", "P1", "P2"]:
            raise ValueError("优先级必须是 P0、P1 或 P2")

        # 构建更新参数
        update_params = {}
        if title is not None:
            update_params['title'] = title
        if priority is not None:
            update_params['priority'] = priority
        if description is not None:
            update_params['description'] = description
        if requires_self_test is not None:
            update_params['requires_self_test'] = requires_self_test

        if not update_params:
            return {
                "status": "error",
                "message": "没有提供要更新的参数",
                "data": None
            }

        manager = TestCaseManager()
        test_case = manager.update_test_case(uuid, **update_params)

        if not test_case:
            return {
                "status": "error",
                "message": "测试用例不存在",
                "data": None
            }

        result = {
            "status": "success",
            "message": "测试用例更新成功",
            "data": {
                "uuid": test_case.uuid,
                "test_case_set_uuid": test_case.test_case_set_uuid,
                "title": test_case.title,
                "priority": test_case.priority.value,
                "requires_self_test": test_case.requires_self_test,
                "description": test_case.description,
                "created_at": test_case.created_at.isoformat() if test_case.created_at else None,
                "updated_at": test_case.updated_at.isoformat() if test_case.updated_at else None
            }
        }

        print(f"[modify_testcase_details] Successfully updated test case: {test_case.title}")
        return result

    except Exception as e:
        error_result = {
            "status": "error",
            "message": f"更新测试用例失败: {str(e)}",
            "data": None
        }
        print(f"[modify_testcase_details] Error: {str(e)}")
        return error_result
