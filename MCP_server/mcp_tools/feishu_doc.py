import requests
import toml
import os
import re

FEISHU_TOKEN_URL = "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal"
FEISHU_DOC_RAW_URL = "https://open.feishu.cn/open-apis/docx/v1/documents/{}/raw_content{}"
FEISHU_WIKI_NODE_URL = "https://open.feishu.cn/open-apis/wiki/v2/spaces/get_node?token={}"  # node_token

CONFIG_PATH = os.path.join(os.path.dirname(__file__), '..', 'feishu_config.toml')

# Read config once at import
with open(CONFIG_PATH, 'r') as f:
    config = toml.load(f)
    APP_ID = config['app_id']
    APP_SECRET = config['app_secret']

def extract_feishu_id_and_type(link: str):
    """
    支持 /doc/xxx, /docx/xxx, /wiki/xxx
    返回 (type, id)
    """
    m = re.search(r"/docx?/([a-zA-Z0-9]+)", link)
    if m:
        return ("doc", m.group(1))
    m = re.search(r"/wiki/([a-zA-Z0-9]+)", link)
    if m:
        return ("wiki", m.group(1))
    return (None, None)

def get_app_access_token():
    data = {"app_id": APP_ID, "app_secret": APP_SECRET}
    resp = requests.post(FEISHU_TOKEN_URL, json=data)
    if resp.status_code != 200:
        return {"error": f"HTTP {resp.status_code}", "detail": resp.text}
    return resp.json()

def get_doc_raw_content(document_id: str, app_access_token: str, lang: int = None):
    lang_param = f"?lang={lang}" if lang is not None else ""
    url = FEISHU_DOC_RAW_URL.format(document_id, lang_param)
    headers = {
        "Authorization": f"Bearer {app_access_token}",
        "Content-Type": "application/json; charset=utf-8"
    }
    resp = requests.get(url, headers=headers)
    if resp.status_code != 200:
        return {"error": f"HTTP {resp.status_code}", "detail": resp.text}
    return resp.json()

def get_wiki_node_content(node_token: str, app_access_token: str):
    url = FEISHU_WIKI_NODE_URL.format(node_token)
    headers = {
        "Authorization": f"Bearer {app_access_token}",
        "Content-Type": "application/json; charset=utf-8"
    }
    resp = requests.get(url, headers=headers)
    if resp.status_code != 200:
        return {"error": f"HTTP {resp.status_code}", "detail": resp.text}
    return resp.json()

def fetch_feishu_doc_content(doc_link: str, lang: int = 0) -> dict:
    doc_type, doc_id = extract_feishu_id_and_type(doc_link)
    if not doc_type or not doc_id:
        return {"error": "Invalid Feishu doc link"}
    token_resp = get_app_access_token()
    if 'app_access_token' not in token_resp:
        return {"error": "Failed to get app_access_token", "detail": token_resp}
    app_access_token = token_resp['app_access_token']
    if doc_type == "doc":
        return get_doc_raw_content(doc_id, app_access_token, lang)
    elif doc_type == "wiki":
        return get_wiki_node_content(doc_id, app_access_token)
    else:
        return {"error": "Unsupported Feishu doc type"}
